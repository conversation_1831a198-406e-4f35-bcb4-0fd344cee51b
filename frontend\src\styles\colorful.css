/* Colorful CSS for Wholesale Management System */

/* Global Styles */
:root {
  --primary-color: #1a365d;
  --primary-hover: #2c5282;
  --secondary-color: #4a5568;
  --success-color: #38a169;
  --danger-color: #e53e3e;
  --warning-color: #dd6b20;
  --info-color: #3182ce;
  --light-bg: #f7fafc;
  --border-color: #e2e8f0;
  --text-dark: #1a202c;
  --text-light: #718096;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --radius: 0.375rem;
  --transition: all 0.2s ease-in-out;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8f9fa;
  color: var(--text-dark);
  line-height: 1.5;
  margin: 0;
  padding: 0;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Navbar */
.navbar {
  background-color: white;
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
  background: linear-gradient(90deg, var(--primary-color), #4299e1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.navbar-links {
  display: flex;
  gap: 1.5rem;
}

.navbar-link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: var(--transition);
}

.navbar-link:hover, .navbar-link.active {
  color: var(--primary-color);
}

.navbar-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--primary-color);
}

.navbar-button {
  background: none;
  border: none;
  color: var(--text-light);
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 0;
  font-family: inherit;
  font-size: inherit;
  transition: var(--transition);
}

.navbar-button:hover {
  color: var(--primary-color);
}

/* Cards */
.card {
  background-color: white;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  background-color: #f8fafc;
}

.card-body {
  padding: 1.5rem;
}

/* Buttons */
.btn {
  display: inline-block;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: var(--radius);
  transition: var(--transition);
  cursor: pointer;
}

.btn-primary {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  color: var(--text-dark);
  background-color: white;
  border-color: var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--light-bg);
}

.btn-success {
  color: white;
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  color: white;
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: white;
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(49, 130, 206, 0.25);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--danger-color);
}

/* Login Page Specific */
.login-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.login-sidebar {
  flex: 1;
  background-color: var(--primary-color);
  color: white;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.login-sidebar h1 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 700;
}

.login-sidebar p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.login-feature {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.login-feature-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.login-form {
  flex: 1;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 2rem;
}

.login-card h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  text-align: center;
}

.login-card p {
  color: var(--text-light);
  margin-bottom: 1.5rem;
  text-align: center;
}

.login-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-btn:hover {
  background-color: var(--primary-hover);
}

/* Dashboard Cards */
.stat-card {
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-blue {
  background: linear-gradient(135deg, #2c5282, #3182ce);
  color: white;
}

.stat-card-red {
  background: linear-gradient(135deg, #c53030, #e53e3e);
  color: white;
}

.stat-card-green {
  background: linear-gradient(135deg, #276749, #38a169);
  color: white;
}

.stat-card-purple {
  background: linear-gradient(135deg, #6b46c1, #805ad5);
  color: white;
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.stat-title {
  font-size: 0.875rem;
  opacity: 0.9;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.stat-link {
  color: white;
  text-decoration: none;
  font-size: 0.875rem;
  opacity: 0.9;
  display: inline-flex;
  align-items: center;
}

.stat-link:hover {
  opacity: 1;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Responsive */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }
  
  .login-sidebar, .login-form {
    padding: 2rem 1rem;
  }
}
